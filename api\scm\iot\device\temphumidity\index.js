import request from "../../../../../utils/request";

// 查询设备温度湿度分页
export function getTempHumidityPageApi(params) {
	return request({
		url: '/scm/device/temp-humidity/page',
		method: 'GET',
		params
	})
}

// 查询设备温度湿度详情
export function getTempHumidityApi(id) {
	return request({
		url: '/scm/device/temp-humidity/get?id=' + id,
		method: 'GET'
	})
}

// 新增设备温度湿度
export function createTempHumidityApi(data) {
	return request({
		url: '/scm/device/temp-humidity/create',
		method: 'POST',
		data
	})
}

// 修改设备温度湿度
export function updateTempHumidityApi(data) {
	return request({
		url: '/scm/device/temp-humidity/update',
		method: 'PUT',
		data
	})
}

// 删除设备温度湿度
export function deleteTempHumidityApi(id) {
	return request({
		url: '/scm/device/temp-humidity/delete?id=' + id,
		method: 'DELETE'
	})
}

// 导出设备温度湿度 Excel
export function exportTempHumidityApi(params) {
	return request({
		url: '/scm/device/temp-humidity/export-excel',
		method: 'GET',
		params,
		responseType: 'blob'
	})
}

// 查询温湿度简单信息
export function getTempHumiditySimpleInfoApi(params) {
	return request({
		url: '/scm/device/temp-humidity/get-simple',
		method: 'GET',
		params
	})
}
