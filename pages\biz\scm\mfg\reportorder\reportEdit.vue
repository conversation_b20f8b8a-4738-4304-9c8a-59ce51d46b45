<template>
	<view class="report-edit-page">
	</view>
</template>

<script>
import { getWorkOrderApi } from '../../../../../api/scm/mfg/workorder'

	export default {
		data() {
			return {
				workId:'',
				reportData:{}
			}
		},
		methods: {
			async initReportOrderData(){
				if(this.workId){
					const workOrderData = await getWorkOrderApi(this.workId)
					this.reportData = {
						workId:this.workId,
						workNo:workOrderData.workNo,
						type:'2',
						startTime:workOrderData.scheduleStartTime || workOrderData.actualStartTime,
						endTime:workOrderData.scheduleEndTime || workOrderData.actualEndTime,
						costTime:'',
						costHeadCount:workOrderData.scheduleHeadCount,
						line:workOrderData.scheduleLine || workOrderData.actualLine,
						quantity:workOrderData.scheduleQuantity,
						piece:workOrderData.schedulePiece,
						batchNo:null,
						
					}
				}
			}
		},
		async onLoad() {
			const eventChannel = this.getOpenerEventChannel()
			if(eventChannel){
				eventChannel.on('acceptDataFormOpener',(data) => {
					console.log('接收到的数据:', data)
					if(data.workId){
						this.workId = data.workId
						this.initReportOrderData()
					}
				})
			} else {
				uni.showToast({
					title: '页面访问异常',
					icon: 'error'
				})
			}
		}
	}
</script>

<style scoped>
</style>
