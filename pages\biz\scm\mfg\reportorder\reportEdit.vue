<template>
	<view class="report-edit-page">
		<!-- 导航栏 -->
		<u-navbar title="报工编辑" :border="true" :background="{ backgroundColor: '#fff' }">
			<view slot="left" @click="goBack">
				<u-icon name="arrow-left" size="20" color="#333"></u-icon>
			</view>
		</u-navbar>

		<!-- 表单内容 -->
		<view class="form-container">
			<uni-forms ref="formRef" :model="formData" :rules="formRules" label-width="100px">
				<!-- 基本信息 -->
				<view class="form-section">
					<view class="section-title">基本信息</view>

					<uni-forms-item label="生产编号" name="workNo" required>
						<uni-easyinput
							v-model="formData.workNo"
							placeholder="请输入生产编号"
							:disabled="true"
							:styles="inputStyles"
						/>
					</uni-forms-item>

					<uni-forms-item label="任务类型" name="type" required>
						<uni-data-select
							v-model="formData.type"
							:localdata="taskTypeOptions"
							placeholder="请选择任务类型"
							:clear="false"
						/>
					</uni-forms-item>

					<uni-forms-item label="报工编号" name="reportCode">
						<uni-easyinput
							v-model="formData.reportCode"
							placeholder="保存自动生成"
							:disabled="true"
							:styles="inputStyles"
						/>
					</uni-forms-item>
				</view>

				<!-- 时间信息 -->
				<view class="form-section">
					<view class="section-title">时间信息</view>

					<uni-forms-item label="开始时间" name="startTime" required>
						<uni-datetime-picker
							v-model="formData.startTime"
							type="datetime"
							:clear-icon="false"
							placeholder="选择开始时间"
							@change="onStartTimeChange"
						/>
					</uni-forms-item>

					<uni-forms-item label="结束时间" name="endTime" required>
						<uni-datetime-picker
							v-model="formData.endTime"
							type="datetime"
							:clear-icon="false"
							placeholder="选择结束时间"
							@change="onEndTimeChange"
						/>
					</uni-forms-item>

					<uni-forms-item label="用时" name="costTime">
						<view class="duration-input-group">
							<uni-easyinput
								v-model="durationHours"
								placeholder="0"
								type="number"
								class="duration-input"
								@input="onDurationChange"
							/>
							<text class="duration-label">小时</text>
							<uni-easyinput
								v-model="durationMinutes"
								placeholder="0"
								type="number"
								class="duration-input"
								@input="onDurationChange"
							/>
							<text class="duration-label">分钟</text>
						</view>
					</uni-forms-item>
				</view>

				<!-- 生产信息 -->
				<view class="form-section">
					<view class="section-title">生产信息</view>

					<uni-forms-item label="人数" name="costHeadCount" required>
						<uni-easyinput
							v-model="formData.costHeadCount"
							placeholder="请输入人数"
							type="number"
							:styles="inputStyles"
						/>
					</uni-forms-item>

					<uni-forms-item label="生产线" name="line" required>
						<uni-data-select
							v-model="formData.line"
							:localdata="lineOptions"
							placeholder="请选择生产线"
							@change="onLineChange"
						/>
					</uni-forms-item>

					<uni-forms-item label="数量" name="quantity" required>
						<view class="quantity-input-group">
							<uni-easyinput
								v-model="formData.quantity"
								placeholder="请输入数量"
								type="number"
								:styles="inputStyles"
							/>
							<text class="unit-label">{{ productUnitName || '件' }}</text>
						</view>
					</uni-forms-item>

					<uni-forms-item label="件数" name="piece">
						<uni-easyinput
							v-model="formData.piece"
							placeholder="请输入件数"
							type="number"
							:styles="inputStyles"
						/>
					</uni-forms-item>

					<uni-forms-item label="批号" name="batchNo">
						<uni-easyinput
							v-model="formData.batchNo"
							placeholder="请输入批号"
							:styles="inputStyles"
						/>
					</uni-forms-item>
				</view>

				<!-- 环境信息 -->
				<view class="form-section">
					<view class="section-title">环境信息</view>

					<uni-forms-item label="温度" name="temperature">
						<uni-easyinput
							v-model="formData.temperature"
							placeholder="请输入温度"
							type="number"
							:styles="inputStyles"
						/>
					</uni-forms-item>

					<uni-forms-item label="湿度" name="humidity">
						<uni-easyinput
							v-model="formData.humidity"
							placeholder="请输入湿度"
							type="number"
							:styles="inputStyles"
						/>
					</uni-forms-item>

					<uni-forms-item label="备注" name="remark">
						<uni-easyinput
							v-model="formData.remark"
							placeholder="请输入备注"
							type="textarea"
							:styles="textareaStyles"
						/>
					</uni-forms-item>
				</view>
			</uni-forms>
		</view>

		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<u-button
				type="default"
				size="large"
				@click="goBack"
				:custom-style="{ marginRight: '20rpx' }"
			>
				取消
			</u-button>
			<u-button
				type="primary"
				size="large"
				:loading="submitLoading"
				@click="submitForm"
			>
				确定
			</u-button>
		</view>

		<!-- 加载提示 -->
		<u-loading-page :loading="pageLoading" loading-text="加载中..."></u-loading-page>
	</view>
</template>

<script>
import { getWorkOrderApi } from '../../../../../api/scm/mfg/workorder'
import { createReportOrderApi, updateReportOrderApi, getReportOrderApi } from '../../../../../api/scm/mfg/reportorder'
import { getDictOptions, DICT_TYPE } from '../../../../../utils/dict'
import { getTempHumiditySimpleInfoApi } from '../../../../../api/scm/iot/device/temphumidity'

export default {
	data() {
		return {
			workId: '',
			reportId: '',
			pageLoading: false,
			submitLoading: false,
			productUnitName: '',
			durationHours: '0',
			durationMinutes: '0',

			// 表单数据
			formData: {
				id: null,
				workId: null,
				workNo: '',
				type: '2',
				reportCode: '',
				startTime: '',
				endTime: '',
				costTime: 0,
				costHeadCount: 1,
				line: '',
				quantity: 0,
				piece: 0,
				batchNo: '',
				temperature: null,
				humidity: null,
				remark: ''
			},

			// 表单验证规则
			formRules: {
				workNo: {
					rules: [{ required: true, errorMessage: '生产编号不能为空' }]
				},
				type: {
					rules: [{ required: true, errorMessage: '任务类型不能为空' }]
				},
				startTime: {
					rules: [{ required: true, errorMessage: '开始时间不能为空' }]
				},
				endTime: {
					rules: [{ required: true, errorMessage: '结束时间不能为空' }]
				},
				costHeadCount: {
					rules: [{ required: true, errorMessage: '人数不能为空' }]
				},
				line: {
					rules: [{ required: true, errorMessage: '生产线不能为空' }]
				},
				quantity: {
					rules: [{ required: true, errorMessage: '数量不能为空' }]
				}
			},

			// 选项数据
			taskTypeOptions: [],
			lineOptions: [],

			// 样式配置
			inputStyles: {
				backgroundColor: '#f8f9fa',
				borderColor: '#e9ecef'
			},
			textareaStyles: {
				backgroundColor: '#f8f9fa',
				borderColor: '#e9ecef',
				minHeight: '80px'
			}
		}
	},

	async onLoad(options) {
		this.pageLoading = true

		try {
			// 初始化字典数据
			await this.initDictData()

			// 处理页面参数
			const eventChannel = this.getOpenerEventChannel()
			if (eventChannel) {
				eventChannel.on('acceptDataFormOpener', (data) => {
					console.log('接收到的数据:', data)
					if (data.workId) {
						this.workId = data.workId
						this.initReportOrderData()
					}
					if (data.reportId) {
						this.reportId = data.reportId
						this.loadReportData()
					}
				})
			} else if (options.workId) {
				this.workId = options.workId
				await this.initReportOrderData()
			} else if (options.reportId) {
				this.reportId = options.reportId
				await this.loadReportData()
			} else {
				uni.showToast({
					title: '页面访问异常',
					icon: 'error'
				})
			}
		} catch (error) {
			console.error('页面初始化失败:', error)
			uni.showToast({
				title: '页面初始化失败',
				icon: 'error'
			})
		} finally {
			this.pageLoading = false
		}
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			try {
				// 获取任务类型字典
				const taskTypes = await getDictOptions(DICT_TYPE.MFG_WORK_TYPE)
				this.taskTypeOptions = taskTypes.map(item => ({
					value: item.value,
					text: item.label
				}))

				// 获取生产线字典
				const lines = await getDictOptions(DICT_TYPE.MANUFACTURE_LINE)
				this.lineOptions = lines.map(item => ({
					value: item.value,
					text: item.label
				}))
			} catch (error) {
				console.error('获取字典数据失败:', error)
			}
		},

		// 初始化报工数据（基于工单）
		async initReportOrderData() {
			if (!this.workId) return

			try {
				const workOrderData = await getWorkOrderApi(this.workId)
				if (workOrderData.code === 0) {
					const data = workOrderData.data
					this.formData = {
						...this.formData,
						workId: this.workId,
						workNo: data.workNo,
						type: '2', // 默认生产类型
						startTime: data.scheduleStartTime || data.actualStartTime || '',
						endTime: data.scheduleEndTime || data.actualEndTime || '',
						costHeadCount: data.scheduleHeadCount || 1,
						line: data.scheduleLine || data.actualLine || '',
						quantity: data.scheduleQuantity || 0,
						piece: data.schedulePiece || 0,
						batchNo: this.generateBatchNo()
					}

					// 计算用时
					this.calculateCostTime()

					// 设置产品单位名称
					this.productUnitName = data.productUnitName || '件'
				}
			} catch (error) {
				console.error('获取工单数据失败:', error)
				uni.showToast({
					title: '获取工单数据失败',
					icon: 'error'
				})
			}
		},

		// 加载报工数据（编辑模式）
		async loadReportData() {
			if (!this.reportId) return

			try {
				const response = await getReportOrderApi(this.reportId)
				if (response.code === 0) {
					const data = response.data
					this.formData = {
						...data,
						startTime: data.startTime || '',
						endTime: data.endTime || ''
					}

					// 处理用时显示
					if (data.costTime) {
						const totalMinutes = parseInt(data.costTime) || 0
						this.updateDurationInputs(totalMinutes)
					}
				}
			} catch (error) {
				console.error('获取报工数据失败:', error)
				uni.showToast({
					title: '获取报工数据失败',
					icon: 'error'
				})
			}
		},

		// 生成批号
		generateBatchNo() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			return `${year}${month}${day}`
		},

		// 开始时间变化
		onStartTimeChange(value) {
			this.formData.startTime = value
			this.calculateCostTime()
		},

		// 结束时间变化
		onEndTimeChange(value) {
			this.formData.endTime = value
			this.calculateCostTime()
		},

		// 计算用时
		calculateCostTime() {
			if (this.formData.startTime && this.formData.endTime) {
				const startTime = new Date(this.formData.startTime)
				const endTime = new Date(this.formData.endTime)

				if (endTime > startTime) {
					const timeDiff = endTime.getTime() - startTime.getTime()
					const minutes = Math.round(timeDiff / (1000 * 60))
					this.formData.costTime = minutes
					this.updateDurationInputs(minutes)
				} else {
					this.formData.costTime = 0
					this.durationHours = '0'
					this.durationMinutes = '0'
				}
			}
		},

		// 更新用时输入框显示
		updateDurationInputs(totalMinutes) {
			const hours = Math.floor(totalMinutes / 60)
			const minutes = totalMinutes % 60
			this.durationHours = hours.toString()
			this.durationMinutes = minutes.toString()
		},

		// 用时手动输入变化
		onDurationChange() {
			const hours = parseInt(this.durationHours) || 0
			const minutes = parseInt(this.durationMinutes) || 0

			// 限制分钟数不超过59
			if (minutes > 59) {
				this.durationMinutes = '59'
				return
			}

			this.formData.costTime = hours * 60 + minutes
		},

		// 生产线变化
		async onLineChange(value) {
			this.formData.line = value
			// 如果有开始时间和结束时间，尝试获取温湿度数据
			if (this.formData.startTime && this.formData.endTime && value) {
				await this.fetchTempHumidityData()
			}
		},

		// 获取温湿度数据
		async fetchTempHumidityData() {
			try {
				if (!this.formData.line || !this.formData.startTime || !this.formData.endTime) {
					return
				}

				// 格式化时间参数
				const formatDateTime = (dateTime) => {
					if (!dateTime) return ''
					const date = new Date(dateTime)
					const year = date.getFullYear()
					const month = String(date.getMonth() + 1).padStart(2, '0')
					const day = String(date.getDate()).padStart(2, '0')
					const hours = String(date.getHours()).padStart(2, '0')
					const minutes = String(date.getMinutes()).padStart(2, '0')
					const seconds = String(date.getSeconds()).padStart(2, '0')
					return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
				}

				const params = {
					line: this.formData.line,
					startTime: formatDateTime(this.formData.startTime),
					endTime: formatDateTime(this.formData.endTime)
				}

				const response = await getTempHumiditySimpleInfoApi(params)
				if (response.code === 0 && response.data) {
					this.formData.temperature = response.data.temperature
					this.formData.humidity = response.data.humidity

					uni.showToast({
						title: '温湿度数据获取成功',
						icon: 'success',
						duration: 1500
					})
				}
			} catch (error) {
				console.error('获取温湿度数据失败:', error)
				// 获取失败不阻止用户操作，用户可以手动输入
			}
		},

		// 提交表单
		async submitForm() {
			try {
				// 表单验证
				const valid = await this.$refs.formRef.validate()
				if (!valid) {
					return
				}

				// 验证结束时间不能早于开始时间
				if (this.formData.startTime && this.formData.endTime) {
					if (new Date(this.formData.endTime) <= new Date(this.formData.startTime)) {
						uni.showToast({
							title: '结束时间不能早于或等于开始时间',
							icon: 'error'
						})
						return
					}
				}

				this.submitLoading = true

				// 准备提交数据
				const submitData = {
					...this.formData,
					costTime: this.formData.costTime.toString(),
					costHeadCount: parseInt(this.formData.costHeadCount) || 0,
					quantity: parseFloat(this.formData.quantity) || 0,
					piece: parseInt(this.formData.piece) || 0,
					temperature: parseFloat(this.formData.temperature) || 0,
					humidity: parseFloat(this.formData.humidity) || 0
				}

				let response
				if (this.reportId) {
					// 编辑模式
					response = await updateReportOrderApi(submitData)
				} else {
					// 新增模式
					response = await createReportOrderApi(submitData)
				}

				if (response.code === 0) {
					uni.showToast({
						title: this.reportId ? '修改成功' : '创建成功',
						icon: 'success'
					})

					// 延迟返回上一页
					setTimeout(() => {
						this.goBack()
					}, 1500)
				} else {
					uni.showToast({
						title: response.msg || '操作失败',
						icon: 'error'
					})
				}
			} catch (error) {
				console.error('提交失败:', error)
				uni.showToast({
					title: '提交失败',
					icon: 'error'
				})
			} finally {
				this.submitLoading = false
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.report-edit-page {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.form-container {
	padding: 20rpx;
}

.form-section {
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	padding-left: 20rpx;
	border-left: 6rpx solid #007aff;
}

.duration-input-group {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex-wrap: wrap;
}

.duration-input {
	flex: 1;
	min-width: 120rpx;
	max-width: 150rpx;
}

.duration-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.quantity-input-group {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.unit-label {
	font-size: 28rpx;
	color: #666;
	padding: 0 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	height: 80rpx;
	line-height: 80rpx;
	border: 1rpx solid #e9ecef;
}

.footer-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx 40rpx;
	border-top: 1rpx solid #e9ecef;
	display: flex;
	gap: 20rpx;
	z-index: 999;
}

.footer-buttons .u-button {
	flex: 1;
}

/* 表单项样式调整 */
::v-deep .uni-forms-item {
	margin-bottom: 30rpx;
}

::v-deep .uni-forms-item__label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

::v-deep .uni-forms-item__content {
	flex: 1;
}

::v-deep .uni-easyinput__content {
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
}

::v-deep .uni-easyinput__content-input {
	font-size: 28rpx;
	color: #333;
}

::v-deep .uni-data-select {
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
}

::v-deep .uni-datetime-picker {
	background-color: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.duration-input-group {
		flex-direction: column;
		align-items: flex-start;
		gap: 15rpx;
	}

	.duration-input {
		max-width: 200rpx;
	}

	.quantity-input-group {
		flex-direction: column;
		align-items: flex-start;
		gap: 15rpx;
	}

	.footer-buttons {
		flex-direction: column;
		gap: 15rpx;
	}
}

/* 加载状态样式 */
.u-loading-page {
	background-color: rgba(255, 255, 255, 0.9);
}
</style>
